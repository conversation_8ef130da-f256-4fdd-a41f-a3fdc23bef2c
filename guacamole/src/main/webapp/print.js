/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Utility function to print PDF blob data
 * @param {string} url - The URL to fetch the PDF from
 * @param {string|FormData} data - The data to send with the request
 * @param {Object} options - Optional configuration object
 * @param {string} options.method - HTTP method (default: 'POST')
 * @param {string} options.contentType - Content type header (default: 'application/x-www-form-urlencoded')
 * @param {Function} options.onSuccess - Success callback
 * @param {Function} options.onError - Error callback
 * @param {boolean} options.showModal - Whether to show loading modal (default: false)
 */
function printPDFBlob(url, data, options) {
    // Set default options
    options = options || {};
    var method = options.method || 'POST';
    var contentType = options.contentType || 'application/x-www-form-urlencoded';
    var onSuccess = options.onSuccess || function() {};
    var onError = options.onError || function(error) { console.error('Print error:', error); };
    var showModal = options.showModal || false;

    // Validate required parameters
    if (!url) {
        onError(new Error('URL is required'));
        return;
    }

    var req = new XMLHttpRequest();
    req.open(method, url, true);
    req.responseType = "blob";

    // Set content type header if data is being sent
    if (data && contentType) {
        req.setRequestHeader("Content-type", contentType);
    }

    // Handle request errors
    req.onerror = function() {
        onError(new Error('Network error occurred while fetching PDF'));
    };

    req.ontimeout = function() {
        onError(new Error('Request timed out'));
    };

    req.onload = function(event) {
        // Check if request was successful
        if (req.status !== 200) {
            onError(new Error('HTTP ' + req.status + ': ' + req.statusText));
            return;
        }

        try {
            // Create blob from response with correct MIME type
            var blob = new Blob([req.response], {type: 'application/pdf'});
            var blobURL = URL.createObjectURL(blob);

            // Create iframe for printing
            var iframe = document.createElement('iframe');
            iframe.src = blobURL;
            iframe.style.position = 'absolute';
            iframe.style.left = '-9999px';
            iframe.style.width = '1px';
            iframe.style.height = '1px';
            iframe.style.border = 'none';

            // Add iframe to document
            document.body.appendChild(iframe);

            // Handle iframe load
            iframe.onload = function() {
                try {
                    // Small delay to ensure PDF is fully loaded
                    setTimeout(function() {
                        iframe.focus();

                        // Try to print the iframe content
                        if (iframe.contentWindow) {
                            iframe.contentWindow.print();
                        } else {
                            // Fallback: open in new window for printing
                            var printWindow = window.open(blobURL, '_blank');
                            if (printWindow) {
                                printWindow.focus();
                                printWindow.print();
                            }
                        }

                        onSuccess();

                        // Clean up after printing
                        setTimeout(function() {
                            if (iframe.parentNode) {
                                document.body.removeChild(iframe);
                            }
                            URL.revokeObjectURL(blobURL);
                        }, 1000);

                    }, 100);
                } catch (printError) {
                    onError(printError);
                    // Clean up on error
                    if (iframe.parentNode) {
                        document.body.removeChild(iframe);
                    }
                    URL.revokeObjectURL(blobURL);
                }
            };

            // Handle iframe load errors
            iframe.onerror = function() {
                onError(new Error('Failed to load PDF in iframe'));
                if (iframe.parentNode) {
                    document.body.removeChild(iframe);
                }
                URL.revokeObjectURL(blobURL);
            };

        } catch (blobError) {
            onError(blobError);
        }
    };

    // Send the request
    try {
        req.send(data);
    } catch (sendError) {
        onError(sendError);
    }
}

/**
 * Alternative function that uses the existing printJS library if available
 * @param {string} url - The URL to fetch the PDF from
 * @param {string|FormData} data - The data to send with the request (optional for GET requests)
 * @param {Object} options - Optional configuration object
 */
function printPDFWithLibrary(url, data, options) {
    options = options || {};

    // Check if printJS library is available
    if (typeof printJS !== 'undefined') {
        // Use the existing printJS library
        printJS({
            printable: url,
            type: 'pdf',
            showModal: options.showModal || false,
            onError: options.onError || function(error) { console.error('Print error:', error); },
            onLoadingStart: options.onLoadingStart,
            onLoadingEnd: options.onLoadingEnd,
            onPrintDialogClose: options.onPrintDialogClose
        });
    } else {
        // Fallback to custom implementation
        printPDFBlob(url, data, options);
    }
}

/**
 * Simple function to print a PDF from a blob URL
 * @param {string} blobURL - The blob URL of the PDF
 * @param {Object} options - Optional configuration object
 */
function printPDFFromBlob(blobURL, options) {
    options = options || {};
    var onSuccess = options.onSuccess || function() {};
    var onError = options.onError || function(error) { console.error('Print error:', error); };

    try {
        // Create iframe for printing
        var iframe = document.createElement('iframe');
        iframe.src = blobURL;
        iframe.style.position = 'absolute';
        iframe.style.left = '-9999px';
        iframe.style.width = '1px';
        iframe.style.height = '1px';
        iframe.style.border = 'none';

        // Add iframe to document
        document.body.appendChild(iframe);

        // Handle iframe load
        iframe.onload = function() {
            try {
                // Small delay to ensure PDF is fully loaded
                setTimeout(function() {
                    iframe.focus();

                    // Try to print the iframe content
                    if (iframe.contentWindow) {
                        iframe.contentWindow.print();
                    } else {
                        // Fallback: open in new window for printing
                        var printWindow = window.open(blobURL, '_blank');
                        if (printWindow) {
                            printWindow.focus();
                            printWindow.print();
                        }
                    }

                    onSuccess();

                    // Clean up after printing
                    setTimeout(function() {
                        if (iframe.parentNode) {
                            document.body.removeChild(iframe);
                        }
                    }, 1000);

                }, 100);
            } catch (printError) {
                onError(printError);
                // Clean up on error
                if (iframe.parentNode) {
                    document.body.removeChild(iframe);
                }
            }
        };

        // Handle iframe load errors
        iframe.onerror = function() {
            onError(new Error('Failed to load PDF in iframe'));
            if (iframe.parentNode) {
                document.body.removeChild(iframe);
            }
        };

    } catch (error) {
        onError(error);
    }
}

// Export functions for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        printPDFBlob: printPDFBlob,
        printPDFWithLibrary: printPDFWithLibrary,
        printPDFFromBlob: printPDFFromBlob
    };
} else if (typeof window !== 'undefined') {
    window.printPDFBlob = printPDFBlob;
    window.printPDFWithLibrary = printPDFWithLibrary;
    window.printPDFFromBlob = printPDFFromBlob;
}
